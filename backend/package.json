{"name": "finsync360-backend", "version": "1.0.0", "description": "FinSync360 Backend API Server", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "build": "echo 'Backend build completed'", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "test:comprehensive": "node tests/runAllTests.js", "test:benchmark": "node tests/performanceBenchmark.js", "test:docs": "node tests/generateApiDocs.js", "test:seed": "node tests/seedTestData.js", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "seed": "node src/scripts/seedDatabase.js", "demo:user": "node scripts/create-demo-user.js", "demo:users": "node scripts/create-demo-users.js", "demo:data": "node scripts/create-demo-data.js", "demo:all": "npm run demo:users && npm run demo:data", "create:admin": "node scripts/create-admin-users.js", "setup:production": "npm run create:admin"}, "dependencies": {"axios": "^1.6.2", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "crypto": "^1.0.1", "dotenv": "^16.3.1", "express": "^4.18.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "fast-xml-parser": "^4.3.2", "handlebars": "^4.7.8", "helmet": "^7.1.0", "hpp": "^0.2.3", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "mongoose": "^8.0.3", "mongoose-paginate-v2": "^1.9.1", "multer": "^1.4.5-lts.1", "net": "^1.0.2", "node-cron": "^3.0.3", "nodemailer": "^6.9.7", "pdf-lib": "^1.17.1", "pdfkit": "^0.14.0", "qrcode": "^1.5.3", "razorpay": "^2.9.2", "sharp": "^0.33.1", "socket.io": "^4.7.4", "speakeasy": "^2.0.0", "twilio": "^4.19.0", "uuid": "^9.0.1", "winston": "^3.11.0", "ws": "^8.14.2", "xml2js": "^0.6.2", "xmlbuilder2": "^3.1.1", "xss": "^1.0.14"}, "devDependencies": {"@types/jest": "^29.5.8", "eslint": "^8.55.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.29.0", "jest": "^29.7.0", "mongodb-memory-server": "^10.1.4", "nock": "^14.0.5", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}, "keywords": ["erp", "api", "tally", "accounting", "nodejs", "express", "mongodb"], "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT"}