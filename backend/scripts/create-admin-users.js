const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// Import models
const User = require('../src/models/User');
const Company = require('../src/models/Company');
const Voucher = require('../src/models/Voucher');
const Item = require('../src/models/Item');
const Party = require('../src/models/Party');

// Connect to database
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    process.exit(1);
  }
};

// Admin and test users data
const adminUsers = [
  {
    name: 'Super Admin',
    email: '<EMAIL>',
    password: 'Admin@123456',
    role: 'super_admin',
    isActive: true,
    isEmailVerified: true,
    permissions: [
      'user_management',
      'company_management',
      'system_settings',
      'view_all_data',
      'export_data',
      'manage_integrations'
    ]
  },
  {
    name: 'System Admin',
    email: '<EMAIL>',
    password: 'SysAdmin@123',
    role: 'admin',
    isActive: true,
    isEmailVerified: true,
    permissions: [
      'user_management',
      'company_management',
      'view_all_data',
      'export_data'
    ]
  },
  {
    name: 'Demo User',
    email: '<EMAIL>',
    password: 'Demo@123456',
    role: 'user',
    isActive: true,
    isEmailVerified: true,
    permissions: [
      'create_company',
      'manage_own_data',
      'tally_integration'
    ]
  }
];

const testUsers = [
  {
    name: 'Test Accountant',
    email: '<EMAIL>',
    password: 'Test@123456',
    role: 'accountant',
    isActive: true,
    isEmailVerified: true,
    permissions: [
      'create_vouchers',
      'manage_inventory',
      'view_reports',
      'tally_sync'
    ]
  },
  {
    name: 'Test Manager',
    email: '<EMAIL>',
    password: 'Manager@123',
    role: 'manager',
    isActive: true,
    isEmailVerified: true,
    permissions: [
      'view_reports',
      'manage_company',
      'user_management',
      'approve_vouchers'
    ]
  },
  {
    name: 'Test Viewer',
    email: '<EMAIL>',
    password: 'Viewer@123',
    role: 'viewer',
    isActive: true,
    isEmailVerified: true,
    permissions: [
      'view_reports',
      'view_vouchers',
      'view_inventory'
    ]
  }
];

// Demo companies data
const demoCompanies = [
  {
    name: 'Demo Trading Company',
    email: '<EMAIL>',
    phone: '+91-**********',
    address: '123 Business Street, Mumbai, Maharashtra 400001',
    gstNumber: '27**********1ZX',
    panNumber: '**********',
    businessType: 'Trading',
    settings: {
      currency: 'INR',
      financialYear: '2024-25',
      gstEnabled: true,
      tallyIntegration: true,
      autoBackup: true
    }
  },
  {
    name: 'Sample Manufacturing Ltd',
    email: '<EMAIL>',
    phone: '+91-9876543211',
    address: '456 Industrial Area, Pune, Maharashtra 411001',
    gstNumber: '27**********1ZY',
    panNumber: '**********',
    businessType: 'Manufacturing',
    settings: {
      currency: 'INR',
      financialYear: '2024-25',
      gstEnabled: true,
      tallyIntegration: true,
      autoBackup: true
    }
  },
  {
    name: 'Test Services Pvt Ltd',
    email: '<EMAIL>',
    phone: '+91-9876543212',
    address: '789 Service Center, Bangalore, Karnataka 560001',
    gstNumber: '29**********1ZZ',
    panNumber: '**********',
    businessType: 'Services',
    settings: {
      currency: 'INR',
      financialYear: '2024-25',
      gstEnabled: true,
      tallyIntegration: false,
      autoBackup: true
    }
  }
];

// Create users function
const createUsers = async (users, userType) => {
  console.log(`\n📝 Creating ${userType} users...`);
  
  for (const userData of users) {
    try {
      // Check if user already exists
      const existingUser = await User.findOne({ email: userData.email });
      if (existingUser) {
        console.log(`⚠️  User ${userData.email} already exists, skipping...`);
        continue;
      }

      // Hash password
      const salt = await bcrypt.genSalt(12);
      const hashedPassword = await bcrypt.hash(userData.password, salt);

      // Create user
      const user = new User({
        ...userData,
        password: hashedPassword,
        createdAt: new Date(),
        updatedAt: new Date()
      });

      await user.save();
      console.log(`✅ Created ${userType} user: ${userData.email}`);
      
      // Store original password for display (don't do this in production!)
      user.originalPassword = userData.password;
      
    } catch (error) {
      console.error(`❌ Error creating user ${userData.email}:`, error.message);
    }
  }
};

// Create companies function
const createCompanies = async () => {
  console.log('\n🏢 Creating demo companies...');
  
  // Get demo user
  const demoUser = await User.findOne({ email: '<EMAIL>' });
  if (!demoUser) {
    console.log('⚠️  Demo user not found, skipping company creation');
    return;
  }

  for (const companyData of demoCompanies) {
    try {
      // Check if company already exists
      const existingCompany = await Company.findOne({ 
        name: companyData.name,
        userId: demoUser._id 
      });
      
      if (existingCompany) {
        console.log(`⚠️  Company ${companyData.name} already exists, skipping...`);
        continue;
      }

      // Create company
      const company = new Company({
        ...companyData,
        userId: demoUser._id,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      });

      await company.save();
      console.log(`✅ Created company: ${companyData.name}`);
      
    } catch (error) {
      console.error(`❌ Error creating company ${companyData.name}:`, error.message);
    }
  }
};

// Main function
const createAdminAndTestUsers = async () => {
  try {
    await connectDB();
    
    console.log('🚀 Starting admin and test user creation...');
    
    // Create admin users
    await createUsers(adminUsers, 'admin');
    
    // Create test users
    await createUsers(testUsers, 'test');
    
    // Create demo companies
    await createCompanies();
    
    console.log('\n🎉 Admin and test users created successfully!');
    console.log('\n📋 Login Credentials:');
    console.log('==========================================');
    
    // Display all created users
    const allUsers = [...adminUsers, ...testUsers];
    allUsers.forEach(user => {
      console.log(`👤 ${user.name} (${user.role})`);
      console.log(`   📧 Email: ${user.email}`);
      console.log(`   🔑 Password: ${user.password}`);
      console.log(`   🎭 Role: ${user.role}`);
      console.log('   ----------------------------------------');
    });
    
    console.log('\n🔐 Security Note:');
    console.log('Please change these default passwords in production!');
    console.log('\n🌐 Access your application at:');
    console.log('Frontend: https://finsync-frontend-62084a54426d.herokuapp.com/');
    console.log('Backend: https://finsync-backend-d34180691b06.herokuapp.com/');
    
  } catch (error) {
    console.error('❌ Error creating users:', error);
  } finally {
    await mongoose.connection.close();
    console.log('\n✅ Database connection closed');
    process.exit(0);
  }
};

// Run the script
if (require.main === module) {
  createAdminAndTestUsers();
}

module.exports = { createAdminAndTestUsers };
